import React, { useState } from 'react';
import { StyleSheet, Platform, Alert, ScrollView, TextInput } from 'react-native';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Ionicons } from '@expo/vector-icons';
import { TouchableOpacity } from 'react-native';
import WebViewSensorTest from '@/components/WebViewSensorTest';

export default function WebViewTestScreen() {
  const [showWebView, setShowWebView] = useState(false);
  const [customUrl, setCustomUrl] = useState('http://localhost:8081');
  const [isEditingUrl, setIsEditingUrl] = useState(false);

  const defaultUrls = [
    {
      name: 'Local Development Server',
      url: 'http://localhost:8081',
      description: 'Your Expo development server'
    },
    {
      name: 'Sensor Test Demo',
      url: 'https://developer.mozilla.org/en-US/docs/Web/API/DeviceMotionEvent',
      description: 'MDN DeviceMotionEvent documentation with examples'
    },
    {
      name: 'Device Orientation Test',
      url: 'https://www.audero.it/demo/device-orientation-api-demo.html',
      description: 'Online device orientation test'
    },
    {
      name: 'WebView Sensor Bridge Test',
      url: 'data:text/html,<!DOCTYPE html><html><head><title>Sensor Test</title><meta name="viewport" content="width=device-width,initial-scale=1"><style>body{font-family:Arial,sans-serif;padding:20px;background:#1a1a2e;color:white;text-align:center}button{background:#4ecdc4;color:white;border:none;padding:15px 30px;margin:10px;border-radius:8px;font-size:16px;cursor:pointer}#data{background:#2d2d44;padding:20px;margin:20px 0;border-radius:8px;font-family:monospace}</style></head><body><h1>🌐 WebView Sensor Test</h1><p>Testing Device Motion API in WebView</p><button onclick="startSensors()">Start Sensors</button><button onclick="stopSensors()">Stop Sensors</button><div id="data">Sensor data will appear here...</div><script>let isRunning=false;function startSensors(){if(typeof DeviceMotionEvent!=="undefined"){if(typeof DeviceMotionEvent.requestPermission==="function"){DeviceMotionEvent.requestPermission().then(response=>{if(response==="granted"){enableSensors()}else{document.getElementById("data").innerHTML="Permission denied"}}).catch(error=>{document.getElementById("data").innerHTML="Permission error: "+error})}else{enableSensors()}}else{document.getElementById("data").innerHTML="DeviceMotionEvent not supported"}}function enableSensors(){isRunning=true;window.addEventListener("devicemotion",handleMotion);window.addEventListener("deviceorientation",handleOrientation);document.getElementById("data").innerHTML="Sensors started..."}function stopSensors(){isRunning=false;window.removeEventListener("devicemotion",handleMotion);window.removeEventListener("deviceorientation",handleOrientation);document.getElementById("data").innerHTML="Sensors stopped"}function handleMotion(event){if(!isRunning)return;const acc=event.acceleration||{x:0,y:0,z:0};document.getElementById("data").innerHTML=`<strong>Accelerometer:</strong><br>X: ${acc.x?.toFixed(3)||"null"}<br>Y: ${acc.y?.toFixed(3)||"null"}<br>Z: ${acc.z?.toFixed(3)||"null"}`}function handleOrientation(event){if(!isRunning)return;document.getElementById("data").innerHTML+=`<br><br><strong>Orientation:</strong><br>Alpha: ${event.alpha?.toFixed(3)||"null"}<br>Beta: ${event.beta?.toFixed(3)||"null"}<br>Gamma: ${event.gamma?.toFixed(3)||"null"}`}</script></body></html>',
      description: 'Embedded sensor test page'
    }
  ];

  const handleUrlSelect = (url: string) => {
    setCustomUrl(url);
    setShowWebView(true);
  };

  const handleCustomUrlSubmit = () => {
    if (customUrl.trim()) {
      setIsEditingUrl(false);
      setShowWebView(true);
    } else {
      Alert.alert('Error', 'Please enter a valid URL');
    }
  };

  if (showWebView) {
    return (
      <ThemedView style={styles.container}>
        <ThemedView style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={() => setShowWebView(false)}>
            <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
          </TouchableOpacity>
          <ThemedText type="title" style={styles.headerTitle}>WebView Sensor Test</ThemedText>
        </ThemedView>
        <WebViewSensorTest url={customUrl} showControls={true} />
      </ThemedView>
    );
  }

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <ThemedView style={styles.header}>
        <ThemedText type="title" style={styles.headerTitle}>🌐 WebView Testing</ThemedText>
        <ThemedText style={styles.headerSubtitle}>
          Test sensor access in WebView containers
        </ThemedText>
      </ThemedView>

      <ThemedView style={styles.section}>
        <ThemedText type="subtitle" style={styles.sectionTitle}>Quick Start URLs</ThemedText>
        <ThemedText style={styles.sectionDescription}>
          Select a URL to test sensor functionality in WebView environment
        </ThemedText>
        
        {defaultUrls.map((item, index) => (
          <TouchableOpacity
            key={index}
            style={styles.urlCard}
            onPress={() => handleUrlSelect(item.url)}
          >
            <ThemedView style={styles.urlCardContent}>
              <ThemedText style={styles.urlName}>{item.name}</ThemedText>
              <ThemedText style={styles.urlDescription}>{item.description}</ThemedText>
              <ThemedText style={styles.urlText} numberOfLines={1}>{item.url}</ThemedText>
            </ThemedView>
            <Ionicons name="chevron-forward" size={24} color="#4ECDC4" />
          </TouchableOpacity>
        ))}
      </ThemedView>

      <ThemedView style={styles.section}>
        <ThemedText type="subtitle" style={styles.sectionTitle}>Custom URL</ThemedText>
        <ThemedText style={styles.sectionDescription}>
          Enter a custom URL to test in WebView
        </ThemedText>
        
        <ThemedView style={styles.customUrlContainer}>
          <TextInput
            style={styles.urlInput}
            value={customUrl}
            onChangeText={setCustomUrl}
            placeholder="Enter URL (e.g., http://localhost:8081)"
            placeholderTextColor="#999999"
            autoCapitalize="none"
            autoCorrect={false}
            keyboardType="url"
          />
          <TouchableOpacity style={styles.goButton} onPress={handleCustomUrlSubmit}>
            <Ionicons name="arrow-forward" size={20} color="#FFFFFF" />
          </TouchableOpacity>
        </ThemedView>
      </ThemedView>

      <ThemedView style={styles.infoSection}>
        <ThemedText type="subtitle" style={styles.infoTitle}>📋 WebView Sensor Testing Guide</ThemedText>
        
        <ThemedView style={styles.infoCard}>
          <ThemedText style={styles.infoCardTitle}>🎯 What This Tests</ThemedText>
          <ThemedText style={styles.infoText}>
            • Device Motion API access in WebView{'\n'}
            • Device Orientation API functionality{'\n'}
            • Sensor permission handling{'\n'}
            • Real vs simulated sensor data
          </ThemedText>
        </ThemedView>

        <ThemedView style={styles.infoCard}>
          <ThemedText style={styles.infoCardTitle}>🔧 Setup Instructions</ThemedText>
          <ThemedText style={styles.infoText}>
            1. Start your Expo development server{'\n'}
            2. Use "Local Development Server" option{'\n'}
            3. Grant sensor permissions when prompted{'\n'}
            4. Test sensor functionality in WebView
          </ThemedText>
        </ThemedView>

        <ThemedView style={styles.infoCard}>
          <ThemedText style={styles.infoCardTitle}>⚠️ Platform Notes</ThemedText>
          <ThemedText style={styles.infoText}>
            • iOS: Requires user gesture for sensor permission{'\n'}
            • Android: May work without explicit permission{'\n'}
            • Web: Limited to simulation in browsers{'\n'}
            • WebView: Best real sensor access
          </ThemedText>
        </ThemedView>
      </ThemedView>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1A1A2E',
  },
  header: {
    paddingTop: 60,
    paddingHorizontal: 20,
    paddingBottom: 30,
    backgroundColor: '#667eea',
    borderBottomLeftRadius: 30,
    borderBottomRightRadius: 30,
    flexDirection: 'row',
    alignItems: 'center',
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
      },
      android: {
        elevation: 8,
      },
      web: {
        boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)',
      },
    }),
  },
  backButton: {
    marginRight: 16,
    padding: 8,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#FFFFFF',
    flex: 1,
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#F0F0F0',
    textAlign: 'center',
    marginTop: 8,
  },
  section: {
    margin: 20,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 8,
  },
  sectionDescription: {
    fontSize: 14,
    color: '#CCCCCC',
    marginBottom: 16,
    lineHeight: 20,
  },
  urlCard: {
    backgroundColor: '#2D2D44',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
  },
  urlCardContent: {
    flex: 1,
  },
  urlName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 4,
  },
  urlDescription: {
    fontSize: 14,
    color: '#CCCCCC',
    marginBottom: 8,
  },
  urlText: {
    fontSize: 12,
    color: '#4ECDC4',
    fontFamily: 'monospace',
  },
  customUrlContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#2D2D44',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
  },
  urlInput: {
    flex: 1,
    padding: 16,
    fontSize: 16,
    color: '#FFFFFF',
  },
  goButton: {
    backgroundColor: '#4ECDC4',
    borderRadius: 8,
    padding: 12,
    margin: 8,
  },
  infoSection: {
    margin: 20,
  },
  infoTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 16,
  },
  infoCard: {
    backgroundColor: '#2D2D44',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
  },
  infoCardTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#4ECDC4',
    marginBottom: 8,
  },
  infoText: {
    fontSize: 14,
    color: '#CCCCCC',
    lineHeight: 20,
  },
});
