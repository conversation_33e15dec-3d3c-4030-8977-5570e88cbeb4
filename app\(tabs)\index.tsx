import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { getEnvironmentType, webViewSensorBridge } from '@/utils/webViewSensorBridge';
import { Ionicons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';
import { Accelerometer, Gyroscope, Magnetometer } from 'expo-sensors';
import React, { useEffect, useState } from 'react';
import { Alert, Dimensions, Platform, ScrollView, StyleSheet, TouchableOpacity } from 'react-native';

const { width } = Dimensions.get('window');

interface SensorData {
  x: number;
  y: number;
  z: number;
}

export default function SensorTestScreen() {
  const [accelerometerData, setAccelerometerData] = useState<SensorData>({ x: 0, y: 0, z: 0 });
  const [gyroscopeData, setGyroscopeData] = useState<SensorData>({ x: 0, y: 0, z: 0 });
  const [magnetometerData, setMagnetometerData] = useState<SensorData>({ x: 0, y: 0, z: 0 });
  const [isAccelerometerActive, setIsAccelerometerActive] = useState(false);
  const [isGyroscopeActive, setIsGyroscopeActive] = useState(false);
  const [isMagnetometerActive, setIsMagnetometerActive] = useState(false);
  const [sensorAvailability, setSensorAvailability] = useState({
    accelerometer: true,
    gyroscope: true,
    magnetometer: true,
  });
  const [isLoading, setIsLoading] = useState(true);
  const [environmentType, setEnvironmentType] = useState<'native' | 'webview' | 'web'>('native');

  useEffect(() => {
    checkSensorAvailability();
    return () => {
      // Cleanup based on environment
      if (environmentType === 'native') {
        Accelerometer.removeAllListeners();
        Gyroscope.removeAllListeners();
        Magnetometer.removeAllListeners();
      } else {
        // Cleanup WebView bridge
        webViewSensorBridge.cleanup();
      }
    };
  }, []);

  const checkSensorAvailability = async () => {
    try {
      console.log('Checking sensor availability...');

      // Detect environment type
      const envType = getEnvironmentType();
      setEnvironmentType(envType);
      console.log('Environment detected:', envType);

      if (envType === 'native') {
        // For native platforms, check actual sensor availability
        const accelerometerAvailable = await Accelerometer.isAvailableAsync();
        const gyroscopeAvailable = await Gyroscope.isAvailableAsync();
        const magnetometerAvailable = await Magnetometer.isAvailableAsync();

        console.log('Native sensor availability:', {
          accelerometer: accelerometerAvailable,
          gyroscope: gyroscopeAvailable,
          magnetometer: magnetometerAvailable,
        });

        setSensorAvailability({
          accelerometer: accelerometerAvailable,
          gyroscope: gyroscopeAvailable,
          magnetometer: magnetometerAvailable,
        });
      } else if (envType === 'webview') {
        // For WebView containers, check Device Motion API support
        const deviceMotionSupported = webViewSensorBridge.isDeviceMotionSupported();
        console.log('WebView environment - Device Motion API supported:', deviceMotionSupported);

        setSensorAvailability({
          accelerometer: true, // Always available (real or simulated)
          gyroscope: true, // Always available (real or simulated)
          magnetometer: true, // Always simulated for web
        });
      } else {
        // For web browsers, use simulation
        console.log('Web browser detected - using simulated sensors');
        setSensorAvailability({
          accelerometer: true,
          gyroscope: true,
          magnetometer: true,
        });
      }
    } catch (error) {
      console.error('Error checking sensor availability:', error);
      // Fallback to simulated sensors on error
      console.log('Falling back to simulated sensors');
      setSensorAvailability({
        accelerometer: true,
        gyroscope: true,
        magnetometer: true,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const toggleAccelerometer = async () => {
    try {
      if (isAccelerometerActive) {
        // Stop sensor based on environment
        if (environmentType === 'native') {
          Accelerometer.removeAllListeners();
          await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
        } else {
          // WebView or web environment
          webViewSensorBridge.stopAccelerometer();
        }
        setIsAccelerometerActive(false);
        console.log('Accelerometer stopped');
      } else {
        console.log('Starting accelerometer...');
        if (!sensorAvailability.accelerometer) {
          Alert.alert(
            'Sensor Not Available',
            environmentType === 'native'
              ? 'This device does not support accelerometer.'
              : 'Accelerometer simulation will be used for web/webview testing.'
          );
          if (environmentType === 'native') return;
        }

        if (environmentType === 'native') {
          // Real sensor for native platforms
          Accelerometer.setUpdateInterval(100);
          Accelerometer.addListener((data) => {
            console.log('Native accelerometer data:', data);
            setAccelerometerData(data);
          });
          setIsAccelerometerActive(true);
          await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
        } else {
          // WebView or web environment
          const success = await webViewSensorBridge.startAccelerometer((data) => {
            console.log('WebView accelerometer data:', data);
            setAccelerometerData(data);
          });

          if (success) {
            setIsAccelerometerActive(true);
          } else {
            Alert.alert('Error', 'Failed to start accelerometer');
          }
        }
        console.log('Accelerometer started');
      }
    } catch (error) {
      console.error('Accelerometer error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      Alert.alert('Error', 'Failed to toggle accelerometer: ' + errorMessage);
    }
  };

  const toggleGyroscope = async () => {
    try {
      if (isGyroscopeActive) {
        // Stop sensor based on environment
        if (environmentType === 'native') {
          Gyroscope.removeAllListeners();
          await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
        } else {
          // WebView or web environment
          webViewSensorBridge.stopGyroscope();
        }
        setIsGyroscopeActive(false);
        console.log('Gyroscope stopped');
      } else {
        console.log('Starting gyroscope...');
        if (!sensorAvailability.gyroscope) {
          Alert.alert(
            'Sensor Not Available',
            environmentType === 'native'
              ? 'This device does not support gyroscope.'
              : 'Gyroscope simulation will be used for web/webview testing.'
          );
          if (environmentType === 'native') return;
        }

        if (environmentType === 'native') {
          // Real sensor for native platforms
          Gyroscope.setUpdateInterval(100);
          Gyroscope.addListener((data) => {
            console.log('Native gyroscope data:', data);
            setGyroscopeData(data);
          });
          setIsGyroscopeActive(true);
          await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
        } else {
          // WebView or web environment
          const success = await webViewSensorBridge.startGyroscope((data) => {
            console.log('WebView gyroscope data:', data);
            setGyroscopeData(data);
          });

          if (success) {
            setIsGyroscopeActive(true);
          } else {
            Alert.alert('Error', 'Failed to start gyroscope');
          }
        }
        console.log('Gyroscope started');
      }
    } catch (error) {
      console.error('Gyroscope error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      Alert.alert('Error', 'Failed to toggle gyroscope: ' + errorMessage);
    }
  };

  const toggleMagnetometer = async () => {
    try {
      if (isMagnetometerActive) {
        // Stop sensor based on environment
        if (environmentType === 'native') {
          Magnetometer.removeAllListeners();
          await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
        } else {
          // WebView or web environment
          webViewSensorBridge.stopMagnetometer();
        }
        setIsMagnetometerActive(false);
        console.log('Magnetometer stopped');
      } else {
        console.log('Starting magnetometer...');
        if (!sensorAvailability.magnetometer) {
          Alert.alert(
            'Sensor Not Available',
            environmentType === 'native'
              ? 'This device does not support magnetometer.'
              : 'Magnetometer simulation will be used for web/webview testing.'
          );
          if (environmentType === 'native') return;
        }

        if (environmentType === 'native') {
          // Real sensor for native platforms
          Magnetometer.setUpdateInterval(100);
          Magnetometer.addListener((data) => {
            console.log('Native magnetometer data:', data);
            setMagnetometerData(data);
          });
          setIsMagnetometerActive(true);
          await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
        } else {
          // WebView or web environment
          const success = await webViewSensorBridge.startMagnetometer((data) => {
            console.log('WebView magnetometer data:', data);
            setMagnetometerData(data);
          });

          if (success) {
            setIsMagnetometerActive(true);
          } else {
            Alert.alert('Error', 'Failed to start magnetometer');
          }
        }
        console.log('Magnetometer started');
      }
    } catch (error) {
      console.error('Magnetometer error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      Alert.alert('Error', 'Failed to toggle magnetometer: ' + errorMessage);
    }
  };

  const testHaptics = async () => {
    try {
      if (Platform.OS === 'web') {
        Alert.alert('Haptic Feedback', 'Haptic feedback is not available in web browsers. Please test on a physical device.');
        return;
      }

      console.log('Testing haptic feedback...');
      await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
      setTimeout(async () => {
        await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      }, 200);
      console.log('Haptic feedback completed');
    } catch (error) {
      console.error('Haptic error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      Alert.alert('Error', 'Failed to test haptic feedback: ' + errorMessage);
    }
  };

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <ThemedView style={styles.header}>
        <ThemedText type="title" style={styles.headerTitle}>📱 Sensor Test Lab</ThemedText>
        <ThemedText style={styles.headerSubtitle}>
          Test your device's sensors • Environment: {environmentType.toUpperCase()}
        </ThemedText>
        {environmentType === 'webview' && (
          <ThemedText style={styles.webViewIndicator}>
            🌐 Running in WebView with Device Motion API support
          </ThemedText>
        )}
      </ThemedView>

      {/* Accelerometer Card */}
      <TouchableOpacity style={[styles.card, styles.accelerometerCard]} onPress={toggleAccelerometer}>
        <ThemedView style={styles.cardHeader}>
          <Ionicons name="speedometer-outline" size={32} color="#FF6B6B" />
          <ThemedText type="subtitle" style={styles.cardTitle}>Accelerometer</ThemedText>
          <ThemedView style={[styles.statusIndicator, { backgroundColor: isAccelerometerActive ? '#4ECDC4' : sensorAvailability.accelerometer ? '#95A5A6' : '#E74C3C' }]} />
        </ThemedView>
        <ThemedText style={styles.cardDescription}>
          {isLoading ? 'Checking availability...' :
           sensorAvailability.accelerometer ?
             `Measures device acceleration • ${environmentType === 'native' ? 'Native sensor' :
               environmentType === 'webview' ? 'WebView with Device Motion API' : 'Simulated data'}` :
           'Not supported on this device'}
        </ThemedText>
        <ThemedView style={styles.dataContainer}>
          <ThemedView style={styles.dataRow}>
            <ThemedText style={styles.dataLabel}>X:</ThemedText>
            <ThemedText style={styles.dataValue}>{accelerometerData.x.toFixed(3)}</ThemedText>
          </ThemedView>
          <ThemedView style={styles.dataRow}>
            <ThemedText style={styles.dataLabel}>Y:</ThemedText>
            <ThemedText style={styles.dataValue}>{accelerometerData.y.toFixed(3)}</ThemedText>
          </ThemedView>
          <ThemedView style={styles.dataRow}>
            <ThemedText style={styles.dataLabel}>Z:</ThemedText>
            <ThemedText style={styles.dataValue}>{accelerometerData.z.toFixed(3)}</ThemedText>
          </ThemedView>
        </ThemedView>
        <ThemedText style={styles.tapHint}>
          {isLoading ? 'Loading...' :
           !sensorAvailability.accelerometer ? 'Not Available' :
           isAccelerometerActive ? 'Tap to stop' : 'Tap to start'}
        </ThemedText>
      </TouchableOpacity>

      {/* Gyroscope Card */}
      <TouchableOpacity style={[styles.card, styles.gyroscopeCard]} onPress={toggleGyroscope}>
        <ThemedView style={styles.cardHeader}>
          <Ionicons name="refresh-circle-outline" size={32} color="#4ECDC4" />
          <ThemedText type="subtitle" style={styles.cardTitle}>Gyroscope</ThemedText>
          <ThemedView style={[styles.statusIndicator, { backgroundColor: isGyroscopeActive ? '#4ECDC4' : '#95A5A6' }]} />
        </ThemedView>
        <ThemedText style={styles.cardDescription}>Measures device rotation</ThemedText>
        <ThemedView style={styles.dataContainer}>
          <ThemedView style={styles.dataRow}>
            <ThemedText style={styles.dataLabel}>X:</ThemedText>
            <ThemedText style={styles.dataValue}>{gyroscopeData.x.toFixed(3)}</ThemedText>
          </ThemedView>
          <ThemedView style={styles.dataRow}>
            <ThemedText style={styles.dataLabel}>Y:</ThemedText>
            <ThemedText style={styles.dataValue}>{gyroscopeData.y.toFixed(3)}</ThemedText>
          </ThemedView>
          <ThemedView style={styles.dataRow}>
            <ThemedText style={styles.dataLabel}>Z:</ThemedText>
            <ThemedText style={styles.dataValue}>{gyroscopeData.z.toFixed(3)}</ThemedText>
          </ThemedView>
        </ThemedView>
        <ThemedText style={styles.tapHint}>
          {isGyroscopeActive ? 'Tap to stop' : 'Tap to start'}
        </ThemedText>
      </TouchableOpacity>

      {/* Magnetometer Card */}
      <TouchableOpacity style={[styles.card, styles.magnetometerCard]} onPress={toggleMagnetometer}>
        <ThemedView style={styles.cardHeader}>
          <Ionicons name="compass-outline" size={32} color="#45B7D1" />
          <ThemedText type="subtitle" style={styles.cardTitle}>Magnetometer</ThemedText>
          <ThemedView style={[styles.statusIndicator, { backgroundColor: isMagnetometerActive ? '#4ECDC4' : '#95A5A6' }]} />
        </ThemedView>
        <ThemedText style={styles.cardDescription}>Measures magnetic field</ThemedText>
        <ThemedView style={styles.dataContainer}>
          <ThemedView style={styles.dataRow}>
            <ThemedText style={styles.dataLabel}>X:</ThemedText>
            <ThemedText style={styles.dataValue}>{magnetometerData.x.toFixed(3)}</ThemedText>
          </ThemedView>
          <ThemedView style={styles.dataRow}>
            <ThemedText style={styles.dataLabel}>Y:</ThemedText>
            <ThemedText style={styles.dataValue}>{magnetometerData.y.toFixed(3)}</ThemedText>
          </ThemedView>
          <ThemedView style={styles.dataRow}>
            <ThemedText style={styles.dataLabel}>Z:</ThemedText>
            <ThemedText style={styles.dataValue}>{magnetometerData.z.toFixed(3)}</ThemedText>
          </ThemedView>
        </ThemedView>
        <ThemedText style={styles.tapHint}>
          {isMagnetometerActive ? 'Tap to stop' : 'Tap to start'}
        </ThemedText>
      </TouchableOpacity>

      {/* Haptics Test Card */}
      <TouchableOpacity style={[styles.card, styles.hapticsCard]} onPress={testHaptics}>
        <ThemedView style={styles.cardHeader}>
          <Ionicons name="phone-portrait-outline" size={32} color="#F39C12" />
          <ThemedText type="subtitle" style={styles.cardTitle}>Haptic Feedback</ThemedText>
          <Ionicons name="hand-left-outline" size={24} color="#F39C12" />
        </ThemedView>
        <ThemedText style={styles.cardDescription}>Test device vibration patterns</ThemedText>
        <ThemedView style={styles.hapticsInfo}>
          <ThemedText style={styles.hapticsText}>Tap to feel the vibration!</ThemedText>
          <ThemedText style={styles.hapticsSubtext}>Heavy impact + Success notification</ThemedText>
        </ThemedView>
      </TouchableOpacity>

      <ThemedView style={styles.footer}>
        <ThemedText style={styles.footerText}>
          🚀 Built with Expo • React Native
        </ThemedText>
      </ThemedView>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1A1A2E',
  },
  header: {
    paddingTop: 60,
    paddingHorizontal: 20,
    paddingBottom: 30,
    backgroundColor: '#667eea',
    borderBottomLeftRadius: 30,
    borderBottomRightRadius: 30,
    // Cross-platform shadow
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
      },
      android: {
        elevation: 8,
      },
      web: {
        boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)',
      },
    }),
  },
  headerTitle: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 8,
    ...Platform.select({
      web: {
        textShadow: '1px 1px 2px rgba(0, 0, 0, 0.3)',
      },
      default: {
        textShadowColor: 'rgba(0, 0, 0, 0.3)',
        textShadowOffset: { width: 1, height: 1 },
        textShadowRadius: 2,
      },
    }),
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#F0F0F0',
    textAlign: 'center',
    opacity: 1,
    ...Platform.select({
      web: {
        textShadow: '1px 1px 2px rgba(0, 0, 0, 0.3)',
      },
      default: {
        textShadowColor: 'rgba(0, 0, 0, 0.3)',
        textShadowOffset: { width: 1, height: 1 },
        textShadowRadius: 2,
      },
    }),
  },
  webViewIndicator: {
    fontSize: 14,
    color: '#4ECDC4',
    textAlign: 'center',
    marginTop: 8,
    fontWeight: '600',
    ...Platform.select({
      web: {
        textShadow: '1px 1px 2px rgba(0, 0, 0, 0.3)',
      },
      default: {
        textShadowColor: 'rgba(0, 0, 0, 0.3)',
        textShadowOffset: { width: 1, height: 1 },
        textShadowRadius: 2,
      },
    }),
  },
  card: {
    marginHorizontal: 20,
    marginVertical: 12,
    padding: 24,
    borderRadius: 20,
    // Cross-platform shadow
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 6 },
        shadowOpacity: 0.15,
        shadowRadius: 12,
      },
      android: {
        elevation: 8,
      },
      web: {
        boxShadow: '0 6px 12px rgba(0, 0, 0, 0.15)',
      },
    }),
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  accelerometerCard: {
    backgroundColor: '#FF6B6B',
  },
  gyroscopeCard: {
    backgroundColor: '#4ECDC4',
  },
  magnetometerCard: {
    backgroundColor: '#45B7D1',
  },
  hapticsCard: {
    backgroundColor: '#F39C12',
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  cardTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
    flex: 1,
    marginLeft: 12,
    ...Platform.select({
      web: {
        textShadow: '1px 1px 3px rgba(0, 0, 0, 0.5)',
      },
      default: {
        textShadowColor: 'rgba(0, 0, 0, 0.5)',
        textShadowOffset: { width: 1, height: 1 },
        textShadowRadius: 3,
      },
    }),
  },
  statusIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    borderWidth: 2,
    borderColor: '#FFFFFF',
  },
  cardDescription: {
    fontSize: 14,
    color: '#F5F5F5',
    marginBottom: 20,
    lineHeight: 20,
    ...Platform.select({
      web: {
        textShadow: '1px 1px 2px rgba(0, 0, 0, 0.4)',
      },
      default: {
        textShadowColor: 'rgba(0, 0, 0, 0.4)',
        textShadowOffset: { width: 1, height: 1 },
        textShadowRadius: 2,
      },
    }),
  },
  dataContainer: {
    backgroundColor: 'rgba(0, 0, 0, 0.2)',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  dataRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  dataLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
    minWidth: 20,
    ...Platform.select({
      web: {
        textShadow: '1px 1px 2px rgba(0, 0, 0, 0.5)',
      },
      default: {
        textShadowColor: 'rgba(0, 0, 0, 0.5)',
        textShadowOffset: { width: 1, height: 1 },
        textShadowRadius: 2,
      },
    }),
  },
  dataValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
    fontFamily: 'monospace',
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 8,
    minWidth: 80,
    textAlign: 'center',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    ...Platform.select({
      web: {
        textShadow: '1px 1px 2px rgba(0, 0, 0, 0.7)',
      },
      default: {
        textShadowColor: 'rgba(0, 0, 0, 0.7)',
        textShadowOffset: { width: 1, height: 1 },
        textShadowRadius: 2,
      },
    }),
  },
  tapHint: {
    fontSize: 14,
    color: '#F0F0F0',
    textAlign: 'center',
    fontStyle: 'italic',
    ...Platform.select({
      web: {
        textShadow: '1px 1px 2px rgba(0, 0, 0, 0.5)',
      },
      default: {
        textShadowColor: 'rgba(0, 0, 0, 0.5)',
        textShadowOffset: { width: 1, height: 1 },
        textShadowRadius: 2,
      },
    }),
  },
  hapticsInfo: {
    alignItems: 'center',
    paddingVertical: 20,
  },
  hapticsText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 8,
    textAlign: 'center',
    ...Platform.select({
      web: {
        textShadow: '1px 1px 3px rgba(0, 0, 0, 0.5)',
      },
      default: {
        textShadowColor: 'rgba(0, 0, 0, 0.5)',
        textShadowOffset: { width: 1, height: 1 },
        textShadowRadius: 3,
      },
    }),
  },
  hapticsSubtext: {
    fontSize: 14,
    color: '#F5F5F5',
    textAlign: 'center',
    ...Platform.select({
      web: {
        textShadow: '1px 1px 2px rgba(0, 0, 0, 0.4)',
      },
      default: {
        textShadowColor: 'rgba(0, 0, 0, 0.4)',
        textShadowOffset: { width: 1, height: 1 },
        textShadowRadius: 2,
      },
    }),
  },
  footer: {
    paddingVertical: 30,
    paddingHorizontal: 20,
    alignItems: 'center',
  },
  footerText: {
    fontSize: 14,
    color: '#BDC3C7',
    textAlign: 'center',
    ...Platform.select({
      web: {
        textShadow: '1px 1px 2px rgba(0, 0, 0, 0.5)',
      },
      default: {
        textShadowColor: 'rgba(0, 0, 0, 0.5)',
        textShadowOffset: { width: 1, height: 1 },
        textShadowRadius: 2,
      },
    }),
  },
});
