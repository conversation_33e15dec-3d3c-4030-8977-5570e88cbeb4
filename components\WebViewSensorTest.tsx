import React, { useRef, useState } from 'react';
import { View, StyleSheet, Alert, Platform, Dimensions } from 'react-native';
import { WebView } from 'react-native-webview';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Ionicons } from '@expo/vector-icons';
import { TouchableOpacity } from 'react-native';

const { width, height } = Dimensions.get('window');

interface WebViewSensorTestProps {
  url?: string;
  showControls?: boolean;
}

export default function WebViewSensorTest({ 
  url = 'http://localhost:8081', 
  showControls = true 
}: WebViewSensorTestProps) {
  const webViewRef = useRef<WebView>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [currentUrl, setCurrentUrl] = useState(url);
  const [canGoBack, setCanGoBack] = useState(false);
  const [canGoForward, setCanGoForward] = useState(false);

  const handleNavigationStateChange = (navState: any) => {
    setCanGoBack(navState.canGoBack);
    setCanGoForward(navState.canGoForward);
    setCurrentUrl(navState.url);
  };

  const handleLoadStart = () => {
    setIsLoading(true);
  };

  const handleLoadEnd = () => {
    setIsLoading(false);
  };

  const handleError = (syntheticEvent: any) => {
    const { nativeEvent } = syntheticEvent;
    console.warn('WebView error: ', nativeEvent);
    Alert.alert(
      'WebView Error',
      `Failed to load: ${nativeEvent.description || 'Unknown error'}\n\nMake sure the development server is running on ${url}`
    );
  };

  const goBack = () => {
    if (webViewRef.current && canGoBack) {
      webViewRef.current.goBack();
    }
  };

  const goForward = () => {
    if (webViewRef.current && canGoForward) {
      webViewRef.current.goForward();
    }
  };

  const reload = () => {
    if (webViewRef.current) {
      webViewRef.current.reload();
    }
  };

  const injectedJavaScript = `
    // Inject sensor permission request helpers
    (function() {
      console.log('WebView sensor bridge injected');
      
      // Override console.log to show in React Native logs
      const originalLog = console.log;
      console.log = function(...args) {
        originalLog.apply(console, args);
        if (window.ReactNativeWebView) {
          window.ReactNativeWebView.postMessage(JSON.stringify({
            type: 'console',
            level: 'log',
            message: args.join(' ')
          }));
        }
      };

      // Auto-request sensor permissions when available
      if (typeof DeviceMotionEvent !== 'undefined' && 
          typeof DeviceMotionEvent.requestPermission === 'function') {
        console.log('Requesting Device Motion permission...');
        DeviceMotionEvent.requestPermission().then(response => {
          console.log('Device Motion permission:', response);
        }).catch(error => {
          console.log('Device Motion permission error:', error);
        });
      }

      if (typeof DeviceOrientationEvent !== 'undefined' && 
          typeof DeviceOrientationEvent.requestPermission === 'function') {
        console.log('Requesting Device Orientation permission...');
        DeviceOrientationEvent.requestPermission().then(response => {
          console.log('Device Orientation permission:', response);
        }).catch(error => {
          console.log('Device Orientation permission error:', error);
        });
      }

      // Test sensor availability
      window.addEventListener('devicemotion', function(event) {
        if (event.acceleration) {
          console.log('Device Motion data available:', {
            x: event.acceleration.x,
            y: event.acceleration.y,
            z: event.acceleration.z
          });
        }
      }, { once: true });

      window.addEventListener('deviceorientation', function(event) {
        console.log('Device Orientation data available:', {
          alpha: event.alpha,
          beta: event.beta,
          gamma: event.gamma
        });
      }, { once: true });

    })();
    true; // Required for injected JavaScript
  `;

  const handleMessage = (event: any) => {
    try {
      const data = JSON.parse(event.nativeEvent.data);
      if (data.type === 'console') {
        console.log(`[WebView ${data.level}]:`, data.message);
      }
    } catch (error) {
      console.log('[WebView]:', event.nativeEvent.data);
    }
  };

  return (
    <ThemedView style={styles.container}>
      {showControls && (
        <ThemedView style={styles.controls}>
          <ThemedText style={styles.urlText} numberOfLines={1}>
            {currentUrl}
          </ThemedText>
          <View style={styles.buttonContainer}>
            <TouchableOpacity 
              style={[styles.button, !canGoBack && styles.buttonDisabled]} 
              onPress={goBack}
              disabled={!canGoBack}
            >
              <Ionicons name="arrow-back" size={20} color={canGoBack ? "#FFFFFF" : "#666666"} />
            </TouchableOpacity>
            <TouchableOpacity 
              style={[styles.button, !canGoForward && styles.buttonDisabled]} 
              onPress={goForward}
              disabled={!canGoForward}
            >
              <Ionicons name="arrow-forward" size={20} color={canGoForward ? "#FFFFFF" : "#666666"} />
            </TouchableOpacity>
            <TouchableOpacity style={styles.button} onPress={reload}>
              <Ionicons name="refresh" size={20} color="#FFFFFF" />
            </TouchableOpacity>
          </View>
        </ThemedView>
      )}
      
      <View style={styles.webViewContainer}>
        {isLoading && (
          <ThemedView style={styles.loadingOverlay}>
            <ThemedText style={styles.loadingText}>Loading WebView...</ThemedText>
            <ThemedText style={styles.loadingSubtext}>
              🌐 Testing sensor access in WebView environment
            </ThemedText>
          </ThemedView>
        )}
        
        <WebView
          ref={webViewRef}
          source={{ uri: currentUrl }}
          style={styles.webView}
          onNavigationStateChange={handleNavigationStateChange}
          onLoadStart={handleLoadStart}
          onLoadEnd={handleLoadEnd}
          onError={handleError}
          onMessage={handleMessage}
          injectedJavaScript={injectedJavaScript}
          javaScriptEnabled={true}
          domStorageEnabled={true}
          allowsInlineMediaPlayback={true}
          mediaPlaybackRequiresUserAction={false}
          allowsFullscreenVideo={true}
          // Enable sensor access
          allowsBackForwardNavigationGestures={true}
          // iOS specific props for sensor access
          {...(Platform.OS === 'ios' && {
            allowsLinkPreview: false,
            dataDetectorTypes: 'none',
          })}
          // Android specific props
          {...(Platform.OS === 'android' && {
            mixedContentMode: 'compatibility',
            thirdPartyCookiesEnabled: true,
          })}
        />
      </View>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1A1A2E',
  },
  controls: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#2D2D44',
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
  },
  urlText: {
    flex: 1,
    fontSize: 14,
    color: '#CCCCCC',
    marginRight: 12,
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: 8,
  },
  button: {
    backgroundColor: '#4ECDC4',
    borderRadius: 8,
    padding: 8,
    minWidth: 36,
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonDisabled: {
    backgroundColor: '#333333',
  },
  webViewContainer: {
    flex: 1,
    position: 'relative',
  },
  webView: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: '#1A1A2E',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  loadingText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 8,
  },
  loadingSubtext: {
    fontSize: 14,
    color: '#4ECDC4',
    textAlign: 'center',
  },
});
