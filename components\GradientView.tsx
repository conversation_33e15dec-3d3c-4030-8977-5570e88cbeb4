import React from 'react';
import { View, ViewStyle } from 'react-native';

interface GradientViewProps {
  colors: string[];
  style?: ViewStyle;
  children?: React.ReactNode;
}

export const GradientView: React.FC<GradientViewProps> = ({ colors, style, children }) => {
  // For now, we'll use the first color as a fallback
  // In a real implementation, you might want to use react-native-linear-gradient
  const fallbackStyle = {
    backgroundColor: colors[0],
    ...style,
  };

  return (
    <View style={fallbackStyle}>
      {children}
    </View>
  );
};
