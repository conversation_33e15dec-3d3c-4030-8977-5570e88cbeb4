import { Platform } from 'react-native';

export interface SensorData {
  x: number;
  y: number;
  z: number;
}

export interface WebViewSensorBridge {
  isWebViewEnvironment: () => boolean;
  isDeviceMotionSupported: () => boolean;
  isRealSensorDataAvailable: () => Promise<boolean>;
  requestDeviceMotionPermission: () => Promise<boolean>;
  startAccelerometer: (callback: (data: SensorData) => void) => Promise<boolean>;
  startGyroscope: (callback: (data: SensorData) => void) => Promise<boolean>;
  startMagnetometer: (callback: (data: SensorData) => void) => Promise<boolean>;
  stopAccelerometer: () => void;
  stopGyroscope: () => void;
  stopMagnetometer: () => void;
  cleanup: () => void;
}

class WebViewSensorBridgeImpl implements WebViewSensorBridge {
  private accelerometerListener: ((event: DeviceMotionEvent) => void) | null = null;
  private gyroscopeListener: ((event: DeviceOrientationEvent) => void) | null = null;
  private magnetometerListener: ((event: DeviceOrientationEvent) => void) | null = null;
  private simulationIntervals: {
    accelerometer?: ReturnType<typeof setInterval>;
    gyroscope?: ReturnType<typeof setInterval>;
    magnetometer?: ReturnType<typeof setInterval>;
  } = {};

  isWebViewEnvironment(): boolean {
    if (Platform.OS !== 'web') return false;
    
    // Check for WebView-specific indicators
    const userAgent = typeof navigator !== 'undefined' ? navigator.userAgent : '';
    const isWebView = 
      // React Native WebView indicators
      userAgent.includes('ReactNativeWebView') ||
      // Android WebView indicators
      userAgent.includes('wv') ||
      userAgent.includes('Version/') && userAgent.includes('Chrome/') && userAgent.includes('Mobile') ||
      // iOS WebView indicators
      userAgent.includes('Mobile/') && !userAgent.includes('Safari/') ||
      // Check for window properties that indicate WebView
      (typeof window !== 'undefined' && (
        // @ts-ignore
        window.ReactNativeWebView ||
        // @ts-ignore
        window.webkit?.messageHandlers ||
        // @ts-ignore
        window.Android
      ));

    return isWebView;
  }

  isDeviceMotionSupported(): boolean {
    return typeof window !== 'undefined' &&
           'DeviceMotionEvent' in window &&
           'DeviceOrientationEvent' in window;
  }

  async isRealSensorDataAvailable(): Promise<boolean> {
    if (!this.isDeviceMotionSupported()) return false;

    try {
      const hasPermission = await this.requestDeviceMotionPermission();
      if (!hasPermission) return false;

      return new Promise((resolve) => {
        let hasMotionData = false;
        let hasOrientationData = false;
        let checkCount = 0;
        const maxChecks = 10;

        const motionListener = (event: DeviceMotionEvent) => {
          if (event.acceleration &&
              (event.acceleration.x !== null || event.acceleration.y !== null || event.acceleration.z !== null)) {
            hasMotionData = true;
          }
        };

        const orientationListener = (event: DeviceOrientationEvent) => {
          if (event.alpha !== null || event.beta !== null || event.gamma !== null) {
            hasOrientationData = true;
          }
        };

        const checkData = () => {
          checkCount++;
          if (hasMotionData && hasOrientationData) {
            cleanup();
            resolve(true);
          } else if (checkCount >= maxChecks) {
            cleanup();
            resolve(false);
          } else {
            setTimeout(checkData, 100);
          }
        };

        const cleanup = () => {
          window.removeEventListener('devicemotion', motionListener);
          window.removeEventListener('deviceorientation', orientationListener);
        };

        window.addEventListener('devicemotion', motionListener);
        window.addEventListener('deviceorientation', orientationListener);

        setTimeout(checkData, 100);
      });
    } catch (error) {
      console.warn('Error checking real sensor data availability:', error);
      return false;
    }
  }

  async requestDeviceMotionPermission(): Promise<boolean> {
    if (!this.isDeviceMotionSupported()) return false;

    try {
      // For iOS 13+ devices, request permission
      if (typeof DeviceMotionEvent !== 'undefined' && 
          // @ts-ignore
          typeof DeviceMotionEvent.requestPermission === 'function') {
        // @ts-ignore
        const permission = await DeviceMotionEvent.requestPermission();
        return permission === 'granted';
      }

      if (typeof DeviceOrientationEvent !== 'undefined' && 
          // @ts-ignore
          typeof DeviceOrientationEvent.requestPermission === 'function') {
        // @ts-ignore
        const permission = await DeviceOrientationEvent.requestPermission();
        return permission === 'granted';
      }

      // For other devices, assume permission is granted
      return true;
    } catch (error) {
      console.warn('Error requesting device motion permission:', error);
      return false;
    }
  }

  async startAccelerometer(callback: (data: SensorData) => void): Promise<boolean> {
    if (this.isDeviceMotionSupported()) {
      try {
        const hasPermission = await this.requestDeviceMotionPermission();
        if (!hasPermission) {
          console.warn('Device motion permission denied, falling back to simulation');
          this.startSimulatedAccelerometer(callback);
          return true;
        }

        // Test if we can get real sensor data
        let hasRealData = false;
        const testListener = (event: DeviceMotionEvent) => {
          if (event.acceleration &&
              (event.acceleration.x !== null || event.acceleration.y !== null || event.acceleration.z !== null)) {
            hasRealData = true;
          }
        };

        window.addEventListener('devicemotion', testListener);

        // Wait a bit to see if we get real data
        await new Promise(resolve => setTimeout(resolve, 500));
        window.removeEventListener('devicemotion', testListener);

        if (hasRealData) {
          this.accelerometerListener = (event: DeviceMotionEvent) => {
            if (event.acceleration) {
              callback({
                x: event.acceleration.x || 0,
                y: event.acceleration.y || 0,
                z: event.acceleration.z || 0,
              });
            }
          };

          window.addEventListener('devicemotion', this.accelerometerListener);
          console.log('Real accelerometer started via Device Motion API');
          return true;
        } else {
          console.warn('No real accelerometer data available, using simulation');
          this.startSimulatedAccelerometer(callback);
          return true;
        }
      } catch (error) {
        console.warn('Error starting real accelerometer:', error);
        this.startSimulatedAccelerometer(callback);
        return true;
      }
    } else {
      this.startSimulatedAccelerometer(callback);
      return true;
    }
  }

  async startGyroscope(callback: (data: SensorData) => void): Promise<boolean> {
    if (this.isDeviceMotionSupported()) {
      try {
        const hasPermission = await this.requestDeviceMotionPermission();
        if (!hasPermission) {
          console.warn('Device motion permission denied, falling back to simulation');
          this.startSimulatedGyroscope(callback);
          return true;
        }

        // Test if we can get real orientation data
        let hasRealData = false;
        const testListener = (event: DeviceOrientationEvent) => {
          if (event.alpha !== null || event.beta !== null || event.gamma !== null) {
            hasRealData = true;
          }
        };

        window.addEventListener('deviceorientation', testListener);

        // Wait a bit to see if we get real data
        await new Promise(resolve => setTimeout(resolve, 500));
        window.removeEventListener('deviceorientation', testListener);

        if (hasRealData) {
          this.gyroscopeListener = (event: DeviceOrientationEvent) => {
            callback({
              x: (event.beta || 0) * (Math.PI / 180), // Convert to rad/s
              y: (event.gamma || 0) * (Math.PI / 180),
              z: (event.alpha || 0) * (Math.PI / 180),
            });
          };

          window.addEventListener('deviceorientation', this.gyroscopeListener);
          console.log('Real gyroscope started via Device Orientation API');
          return true;
        } else {
          console.warn('No real orientation data available, using simulation');
          this.startSimulatedGyroscope(callback);
          return true;
        }
      } catch (error) {
        console.warn('Error starting real gyroscope:', error);
        this.startSimulatedGyroscope(callback);
        return true;
      }
    } else {
      this.startSimulatedGyroscope(callback);
      return true;
    }
  }

  async startMagnetometer(callback: (data: SensorData) => void): Promise<boolean> {
    // Magnetometer is not directly available via web APIs
    // Fall back to simulation
    this.startSimulatedMagnetometer(callback);
    return true;
  }

  private startSimulatedAccelerometer(callback: (data: SensorData) => void): void {
    const simulateData = () => {
      const data = {
        x: (Math.random() - 0.5) * 2,
        y: (Math.random() - 0.5) * 2,
        z: (Math.random() - 0.5) * 2 + 9.8, // Include gravity
      };
      callback(data);
    };

    simulateData(); // Initial data
    this.simulationIntervals.accelerometer = setInterval(simulateData, 100);
    console.log('Simulated accelerometer started');
  }

  private startSimulatedGyroscope(callback: (data: SensorData) => void): void {
    const simulateData = () => {
      const data = {
        x: (Math.random() - 0.5) * 4, // Angular velocity in rad/s
        y: (Math.random() - 0.5) * 4,
        z: (Math.random() - 0.5) * 4,
      };
      callback(data);
    };

    simulateData(); // Initial data
    this.simulationIntervals.gyroscope = setInterval(simulateData, 100);
    console.log('Simulated gyroscope started');
  }

  private startSimulatedMagnetometer(callback: (data: SensorData) => void): void {
    const simulateData = () => {
      const data = {
        x: (Math.random() - 0.5) * 100, // Magnetic field in µT
        y: (Math.random() - 0.5) * 100,
        z: (Math.random() - 0.5) * 100 + 50, // Earth's magnetic field
      };
      callback(data);
    };

    simulateData(); // Initial data
    this.simulationIntervals.magnetometer = setInterval(simulateData, 100);
    console.log('Simulated magnetometer started');
  }

  stopAccelerometer(): void {
    if (this.accelerometerListener) {
      window.removeEventListener('devicemotion', this.accelerometerListener);
      this.accelerometerListener = null;
    }
    if (this.simulationIntervals.accelerometer) {
      clearInterval(this.simulationIntervals.accelerometer);
      this.simulationIntervals.accelerometer = undefined;
    }
  }

  stopGyroscope(): void {
    if (this.gyroscopeListener) {
      window.removeEventListener('deviceorientation', this.gyroscopeListener);
      this.gyroscopeListener = null;
    }
    if (this.simulationIntervals.gyroscope) {
      clearInterval(this.simulationIntervals.gyroscope);
      this.simulationIntervals.gyroscope = undefined;
    }
  }

  stopMagnetometer(): void {
    if (this.magnetometerListener) {
      window.removeEventListener('deviceorientation', this.magnetometerListener);
      this.magnetometerListener = null;
    }
    if (this.simulationIntervals.magnetometer) {
      clearInterval(this.simulationIntervals.magnetometer);
      this.simulationIntervals.magnetometer = undefined;
    }
  }

  cleanup(): void {
    this.stopAccelerometer();
    this.stopGyroscope();
    this.stopMagnetometer();
  }
}

// Singleton instance
export const webViewSensorBridge = new WebViewSensorBridgeImpl();

// Helper function to detect environment type
export const getEnvironmentType = (): 'native' | 'webview' | 'web' => {
  if (Platform.OS !== 'web') return 'native';
  if (webViewSensorBridge.isWebViewEnvironment()) return 'webview';
  return 'web';
};
