{"expo": {"name": "santri-mental", "slug": "santri-mental", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "santrimental", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true, "infoPlist": {"NSMotionUsageDescription": "This app uses device motion sensors to test sensor functionality in WebView environments.", "NSLocationWhenInUseUsageDescription": "This app may request location for comprehensive sensor testing."}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "permissions": ["android.permission.INTERNET", "android.permission.ACCESS_NETWORK_STATE", "android.permission.VIBRATE"]}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png", "config": {"firebase": {"measurementId": "G-0000000000"}}}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}], ["react-native-webview", {"allowsInlineMediaPlayback": true, "mediaPlaybackRequiresUserAction": false, "allowsFullscreenVideo": true}]], "experiments": {"typedRoutes": true}, "extra": {"router": {}, "eas": {"projectId": "69f4110c-8221-4436-8e9b-0751b3bbf05b"}}, "owner": "roniwahyu1"}}