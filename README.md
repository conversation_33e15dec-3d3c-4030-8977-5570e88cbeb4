# 📱 Sensor Test Lab - React Native Expo App

A comprehensive React Native Expo app for testing device sensors with **WebView container support** and real Device Motion API integration. Features vibrant design, responsive layout, intuitive interface, and high contrast colors.

## ✨ Features

### 🔬 Sensor Testing
- **Accelerometer**: Real-time acceleration data (X, Y, Z axes)
- **Gyroscope**: Device rotation measurements
- **Magnetometer**: Magnetic field detection
- **Haptic Feedback**: Test device vibration patterns

### 🌐 WebView Container Support
- **Device Motion API Integration**: Access real device sensors in WebView
- **Automatic Environment Detection**: Native vs WebView vs Web browser
- **Permission Handling**: Automatic sensor permission requests for iOS 13+
- **Fallback Simulation**: Simulated sensor data when real sensors unavailable
- **WebView Testing Interface**: Built-in testing with custom URLs

### 📊 Device Information
- Complete device specifications
- Screen dimensions and pixel density
- Sensor availability status
- App and platform details
- Performance optimization tips

### 🎨 Design Features
- **Vibrant Colors**: High contrast, accessible color scheme
- **Card-Based Layout**: Clean, modern interface
- **Responsive Design**: Adapts to different screen sizes
- **Intuitive Controls**: Tap to start/stop sensors
- **Real-time Updates**: Live sensor data at 100ms intervals

## 🚀 Getting Started

### Prerequisites
- Node.js (v16 or higher)
- Expo CLI
- Android device or emulator
- Expo Go app (for testing)

### Installation

1. **Clone and install dependencies**
   ```bash
   cd santri-mental
   npm install
   ```

2. **Start the development server**
   ```bash
   npm start
   ```

3. **Run on device**
   - Scan QR code with Expo Go app (Android)
   - Or press `a` to open Android emulator
   - Or press `w` to open in web browser

## 📱 Usage

### Sensor Tab
- **Tap any sensor card** to start/stop data collection
- **Watch real-time values** update as you move your device
- **Status indicators** show active sensors (green = active, gray = inactive)
- **Haptic feedback** provides tactile confirmation

### Device Info Tab
- **View device specifications** including brand, model, OS
- **Check sensor availability** on your specific device
- **Monitor screen properties** like resolution and pixel density
- **Read performance tips** for optimal sensor usage

### WebView Tab
- **Test sensor access in WebView containers**
- **Select from predefined URLs** or enter custom URL
- **Grant sensor permissions** when prompted (iOS 13+)
- **Compare WebView vs Native** sensor performance
- **Built-in navigation controls** and error handling

## 🛠️ Technical Details

### Dependencies
- **expo-sensors**: Core sensor functionality
- **expo-device**: Device information
- **expo-haptics**: Vibration feedback
- **expo-constants**: App configuration
- **@expo/vector-icons**: Icon library

### Architecture
- **File-based routing** with Expo Router
- **TypeScript** for type safety
- **Themed components** for consistent styling
- **Responsive design** with Dimensions API

### Sensor Implementation
```typescript
// Example: Accelerometer usage
const [data, setData] = useState({ x: 0, y: 0, z: 0 });

const startAccelerometer = async () => {
  const isAvailable = await Accelerometer.isAvailableAsync();
  if (isAvailable) {
    Accelerometer.setUpdateInterval(100);
    Accelerometer.addListener(setData);
  }
};
```

## 🎯 Testing on Physical Device

For best results, test on a physical Android device:

1. **Install Expo Go** from Google Play Store
2. **Connect to same WiFi** as development machine
3. **Scan QR code** from terminal
4. **Move device** to see sensor values change
5. **Test haptic feedback** by tapping cards

## 🔧 Customization

### Colors
Edit vibrant color scheme in component styles:
```typescript
const colors = {
  accelerometer: '#FF6B6B',  // Red
  gyroscope: '#4ECDC4',      // Teal
  magnetometer: '#45B7D1',   // Blue
  haptics: '#F39C12',        // Orange
  header: '#667eea'          // Purple
};
```

### Sensor Update Rate
Adjust sensor refresh rate:
```typescript
Accelerometer.setUpdateInterval(100); // 100ms = 10Hz
```

## 📋 Project Structure

```
santri-mental/
├── app/
│   ├── (tabs)/
│   │   ├── index.tsx      # Sensor testing screen
│   │   ├── explore.tsx    # Device info screen
│   │   └── _layout.tsx    # Tab navigation
│   └── _layout.tsx        # Root layout
├── components/
│   ├── ThemedText.tsx     # Styled text components
│   ├── ThemedView.tsx     # Styled view components
│   └── GradientView.tsx   # Custom gradient component
└── constants/
    └── Colors.ts          # Color definitions
```

## 🚀 Built With

- **Expo SDK 53**: Cross-platform development framework
- **React Native 0.79**: Mobile app framework
- **TypeScript**: Type-safe JavaScript
- **Expo Router**: File-based navigation
- **Expo Sensors**: Native sensor access

## 📄 License

This project is open source and available under the MIT License.

---

**🎉 Happy Testing!** Move your device around and explore the amazing sensors built into modern smartphones!
